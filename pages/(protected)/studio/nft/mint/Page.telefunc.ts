import { sign } from 'hono/jwt'
import { tryCatch } from '@/lib/try-catch'
import { MINT_SESSION_SECRET } from '@/server/config/auth.config'
import { getUser } from '@/server/helpers/getUser'
import { prisma } from '@/server/lib/prismaClient'

export interface MintNftParams {
	name: string
	description: string
	royaltyBasisPoints: number
	metadataUri: string
	collectionId: string | null
	transactionHash: string
	nftAddress: string
	nftImage: string
}

export async function onMintNft({
	name,
	description,
	royaltyBasisPoints,
	metadataUri,
	collectionId,
	transactionHash,
	nftAddress,
	nftImage,
}: MintNftParams) {
	const user = getUser()
	try {
		// Prepare base data object
		const nftData = {
			name,
			description,
			metadataUrl: metadataUri,
			publicKey: nftAddress,
			imageUrl: nftImage,
			royaltyBasisPoints,
			owner: {
				connect: { id: user.userId },
			},
			creator: {
				connect: { id: user.userId },
			},
			activityLogs: {
				create: {
					type: 'NFT_MINTED',
					transactionHash,
				},
			},
		} as const

		// Only connect to a collection if collectionId is provided and not null or empty
		const createData =
			collectionId && collectionId.trim() !== ''
				? {
						...nftData,
						collection: {
							connect: { id: collectionId },
						},
					}
				: nftData

		const nft = await prisma.nFT.create({
			data: createData,
		})

		return { success: true, nft }
	} catch (error) {
		console.error('Error minting NFT:', error)
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Unknown error occurred',
		}
	}
}

export async function onGetUserCollections() {
	const user = getUser()
	const collections = await prisma.collection.findMany({
		select: {
			id: true,
			publicKey: true,
			name: true,
			logoUrl: true,
			royaltyBasisPoints: true,
		},
		where: {
			ownerId: user.userId,
		},
	})
	return collections
}

export async function onGetOneTimeAccessTokenForMinting() {
	const user = getUser()

	const { data: mintingSession, error: mintSessionError } = await tryCatch(
		prisma.mintingSession.create({
			data: {
				sessionKey: crypto.randomUUID(),
				userId: user.userId,
				active: true,
			},
		}),
	)

	if (mintSessionError) {
		return {
			success: false,
			message: 'Minting session creation failed',
			data: null,
			error: mintSessionError,
		}
	}

	const sessionTokenPayload = {
		publicKey: user.publicKey,
		userId: user.userId,
		sessionId: mintingSession.id,
	}

	const { data: mintToken, error: minTokenSignError } = await tryCatch(
		sign(sessionTokenPayload, MINT_SESSION_SECRET),
	)

	if (minTokenSignError) {
		return {
			success: false,
			message: 'Minting session creation failed',
			data: null,
			error: minTokenSignError,
		}
	}

	return {
		success: true,
		message: 'Minting session created successfully',
		data: {
			mintToken: mintToken,
		},
	}
}
