import {
	create,
	fetchCollection,
	ruleSet,
	updateCollection,
} from '@metaplex-foundation/mpl-core'
import { generateSigner, publicKey } from '@metaplex-foundation/umi'
import { base58 } from '@metaplex-foundation/umi/serializers'
import { useWallet } from '@solana/wallet-adapter-react'
import { Download } from 'lucide-react'
import { ofetch } from 'ofetch'
import { useContext, useEffect, useState } from 'react'
import { toast } from 'sonner'
import { navigate } from 'vike/client/router'
import { useData } from 'vike-react/useData'
import { usePageContext } from 'vike-react/usePageContext'
import FileInput from '@/components/atoms/FileInput'
import FolderInput from '@/components/atoms/FolderInput'
import RadioSwitch from '@/components/atoms/RadioSwitch'
import customToast from '@/components/CustomToast'
import NftCollectionTable from '@/components/NFtCollectionTable'
import type { Attribute } from '@/components/studio'
import {
	AttributeEditor,
	FileUpload,
	Form<PERSON>ontainer,
	StudioHeader,
} from '@/components/studio'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { useMintToken } from '@/hooks/useMintToken'
import useUpdateAuthority from '@/hooks/useUpdateAuthority'
import useUploadMint from '@/hooks/useUploadMint'
import { tryCatch } from '@/lib/try-catch'
import { UmiContext } from '@/lib/umi'
import { clog, parseCSVToJSON } from '@/lib/utils'
import { type BulkNftItem, convertToBulkNftItems } from '@/types/bulk-nft.types'
import type { UploadData } from '@/types/UploadData'
import CollectionDropdown from '../CollectionDropdown'
import type { Collection, CollectionList } from './+data.client'
import { onMintNft } from './Page.telefunc'

type SwitchType = 'single-nft' | 'bulk-upload'
const uploadSwitchValues: { label: string; value: SwitchType }[] = [
	{ label: 'Single NFT', value: 'single-nft' },
	{ label: 'Bulk Upload', value: 'bulk-upload' },
]

export default function CreateNFTPage() {
	const data = useData<CollectionList[]>()
	const pageContext = usePageContext()
	const umi = useContext(UmiContext)
	const wallet = useWallet()
	const [nftImage, setNftImage] = useState<File>()
	const [title, setTitle] = useState('')
	const [description, setDescription] = useState('')
	const [attributes, setAttributes] = useState<Attribute[]>([])
	const [royaltyPercentage, setRoyaltyPercentage] = useState('10')
	const [isLoading, setIsLoading] = useState(false)
	const [selectedCollection, setSelectedCollection] =
		useState<Collection | null>(null)
	const [transactionHash, setTransactionHash] = useState('')
	const [activeUploadType, setActiveUploadType] =
		useState<SwitchType>('bulk-upload')
	const [csvFile, setCsvFile] = useState<File | null>(null)
	const [folderFiles, setFolderFiles] = useState<FileList | null>(null)
	const [parsedCsvData, setParsedCsvData] = useState<
		Record<string, string | number>[]
	>([])
	// State to store processed bulk NFT items
	const [bulkNftItems, setBulkNftItems] = useState<BulkNftItem[]>([])

	const updateAuthorityMutation = useUpdateAuthority()
	const { mutateAsync: getMintToken } = useMintToken()

	useEffect(() => {
		const csvDataCount = parsedCsvData.length
		const folderFilesCount = folderFiles ? folderFiles.length : 0

		if (csvDataCount > 0 && folderFilesCount > 0) {
			clog('=== MERGING CSV DATA WITH FOLDER FILES ===')

			// Create a map of file names for quick lookup
			const fileMap = Array.from(folderFiles || []).reduce(
				(map, file) => {
					map[file.name] = file
					return map
				},
				{} as Record<string, File>,
			)

			// Merge CSV data with files - always use imageId directly
			const merged = parsedCsvData.map((csvRecord) => {
				const imageId = csvRecord.imageId as string
				const matchedFile = fileMap[imageId]

				return {
					metadata: {
						...csvRecord,
						id: Number(csvRecord.id),
					},
					file: matchedFile || null,
				}
			})
			// Filter out incomplete entries (those without a matched file)
			const filteredMerged = merged.filter((item) => item.file !== null)
			// Convert to BulkNftItems format
			const nftItems = convertToBulkNftItems(filteredMerged)
			setBulkNftItems(nftItems)
		}
	}, [parsedCsvData, folderFiles])

	// Handle CSV file selection and parse it to JSON
	const handleCsvFileSelect = async (file: File | null) => {
		setCsvFile(file)

		if (file) {
			try {
				// Read the file content
				const text = await file.text()

				// Parse CSV to JSON
				const jsonData = parseCSVToJSON(text)

				// Update state - useEffect will log when both states have data
				setParsedCsvData(jsonData)
			} catch (error) {
				console.error('Error parsing CSV file:', error)
				customToast.error('Failed to parse CSV file. Please check the format.')
				setParsedCsvData([])
			}
		} else {
			// Reset parsed data when file is cleared
			setParsedCsvData([])
		}
	}

	const handleFolderFilesSelect = (files: FileList | null) => {
		// Update state - useEffect will log when both states have data
		setFolderFiles(files)
	}

	const addAttribute = () => {
		const newId = crypto.randomUUID()
		setAttributes([...attributes, { id: newId, traitType: '', value: '' }])
	}

	const removeAttribute = (id: string) => {
		setAttributes(attributes.filter((attr) => attr.id !== id))
	}

	const updateAttribute = (
		id: string,
		field: 'traitType' | 'value',
		value: string,
	) => {
		setAttributes(
			attributes.map((attr) =>
				attr.id === id ? { ...attr, [field]: value } : attr,
			),
		)
	}

	const handleCreateNFT = async () => {
		if (!nftImage) {
			customToast.error('Please upload an image for your NFT')
			return
		}

		if (!title) {
			customToast.error('Please provide a title for your NFT')
			return
		}

		if (!description || description.length < 250) {
			customToast.error(
				'Please provide minimum 250 characters description for your NFT',
			)
			return
		}

		if (!royaltyPercentage) {
			customToast.error('Please provide a royalty percentage for your NFT')
			return
		}

		if (!wallet.publicKey || !wallet.wallet) {
			customToast.error('Please connect your wallet first')
			return
		}

		setIsLoading(true)

		try {
			// Here would be the actual implementation for creating an NFT
			const formData = new FormData()

			// Add all form fields to the formData
			formData.append('title', title)
			formData.append('description', description)
			formData.append('royaltyPercentage', royaltyPercentage)
			formData.append('attributes', JSON.stringify(attributes))

			// For image, we need to convert the data URL to a file object
			// This assumes nftImage is a data URL or URL string
			if (nftImage) {
				formData.append('file', nftImage)
			}

			const { data: mintTokenResponse, error: mintTokenError } = await tryCatch(
				getMintToken(),
			)

			if (mintTokenError) {
				customToast.error('Failed to generate mint token')
				console.error('Mint token error:', mintTokenError)
				return
			}

			// Send the request to get metadata URL
			const backendResponse = await ofetch<UploadData>(
				`${import.meta.env.PUBLIC_ENV__NFT_UPLOAD_URL}/upload/nft`,
				{
					method: 'POST',
					body: formData,
					headers: {
						Authorization: `Bearer ${mintTokenResponse.data?.mintToken}`,
					},
				},
			)

			if (!backendResponse || !backendResponse.success) {
				throw new Error('Failed to process NFT data on backend')
			}
			if (!umi || !wallet.publicKey || !wallet.signTransaction) {
				throw new Error(
					'Wallet not connected or fee collector address not set.',
				)
			}
			// 2. Create a signer for the asset
			const assetSigner = generateSigner(umi)

			// Create NFT creation parameters
			const createParams: Parameters<typeof create>[1] = {
				asset: assetSigner,
				name: title,
				uri: backendResponse.data.metadataUri,
				plugins: [
					{
						type: 'Royalties',
						basisPoints: Number.parseInt(royaltyPercentage) * 100,
						creators: [
							{
								address: publicKey(wallet.publicKey),
								percentage: 100,
							},
						],
						ruleSet: ruleSet('None'), // Compatibility rule set
					},
				],
			}

			// Only add collection if one is selected
			if (selectedCollection) {
				// fetch the collection
				const collection = await fetchCollection(
					umi,
					publicKey(selectedCollection.publicKey),
				)
				// Add collection to creation parameters
				createParams.collection = collection
			}

			// Create the asset
			const { signature } = await create(umi, createParams).sendAndConfirm(umi)

			const serializedSignature = base58.deserialize(signature)[0]
			setTransactionHash(serializedSignature)

			// Now call the telefunc endpoint with the metadata URL
			const response = await onMintNft({
				collectionId: selectedCollection?.id || '',
				description,
				name: title,
				royaltyBasisPoints: Number.parseInt(royaltyPercentage) * 100,
				transactionHash: serializedSignature,
				nftAddress: assetSigner.publicKey.toString(),
				nftImage: backendResponse.data.fileUrl || '',
				metadataUri: backendResponse.data.metadataUri,
			})

			if (response.success) {
				customToast.success('NFT minted successfully!')
				navigate('/studio/nft/success', {
					pageContext: {
						mint: {
							nft: {
								address: assetSigner.publicKey.toString(),
								transactionHash: serializedSignature,
							},
						},
						collection: selectedCollection
							? {
									address: selectedCollection.publicKey,
								}
							: null,
					},
				})
			} else {
				customToast.error('Failed to mint NFT')
				console.error('NFT minting error:', response.error)
			}
		} catch (error) {
			customToast.error('Failed to create NFT')
			console.error('NFT creation error:', error)
			// Navigate to error page
			navigate('/studio/nft/failure', {
				pageContext: {
					error: {
						message:
							error instanceof Error ? error.message : 'Unknown error occurred',
					},
				},
			})
		} finally {
			setIsLoading(false)
		}
	}

	const { mutateAsync: bulkUploadNFTs } = useUploadMint()

	const handleBulkUpload = async () => {
		if (!wallet.publicKey || !wallet.wallet) {
			customToast.error('Please connect your wallet first')
			return
		}

		if (!selectedCollection) {
			customToast.error('Please select a collection for your NFTs')
			return
		}

		// Check if we have any bulk NFT items
		if (bulkNftItems.length === 0) {
			clog('Bulk upload aborted: No valid NFT data to upload')
			toast.error(
				'No valid NFT data to upload. Please check your CSV and image files.',
			)
			return
		}
		setIsLoading(true)

		if (!umi) {
			customToast.error('Umi not initialized')
			return
		}

		try {
			// Count pending items
			const pendingItems = bulkNftItems.filter(
				(nft) => nft.status === 'pending',
			).length
			clog(`Starting upload of ${pendingItems} pending NFTs`)
			const server_authority =
				import.meta.env.PUBLIC_ENV__SERVER_AUTHORITY ||
				'HMBQoRDK8ZxvycucQUifvqnqy8tv3pXVGCRPDafEs21j'
			const collection = await fetchCollection(
				umi,
				publicKey(selectedCollection.publicKey),
			)
			if (collection.updateAuthority.toString() !== server_authority) {
				// await addPlugin(umi, {
				// 	asset: publicKey(selectedCollection.publicKey),
				// 	plugin: {
				// 		type: 'UpdateDelegate',
				// 		authority: {
				// 			type: 'Address',
				// 			address: publicKey(server_authority),
				// 		},
				// 		additionalDelegates: [],
				// 	},
				// }).sendAndConfirm(umi)
				await updateCollection(umi, {
					collection: publicKey(selectedCollection.publicKey),
					newUpdateAuthority: publicKey(server_authority),
					authority: umi.identity,
				}).sendAndConfirm(umi, {
					send: {
						skipPreflight: true,
					},
				})
				// wait for 10 seconds
				await new Promise((resolve) => setTimeout(resolve, 6000))
			}

			for (const nft of bulkNftItems) {
				if (nft.status === 'pending') {
					// Log each NFT being processed
					clog(`Processing NFT #${nft.id}: ${nft.name}`)

					setBulkNftItems((prev) =>
						prev.map((nftItem) => {
							if (nftItem.id === nft.id) {
								return { ...nftItem, status: 'processing' }
							}
							return nftItem
						}),
					)

					const { data, error } = await tryCatch(
						bulkUploadNFTs({
							index: nft.id,
							file: nft.image,
							title: nft.name,
							description: nft.description,
							attributes: Object.entries(nft.attributes).map(
								([key, value]) => ({
									trait_type: key,
									value: value.toString(),
								}),
							),
							royaltyPercentage: selectedCollection.royaltyBasisPoints / 100,
							owner: wallet.publicKey.toString(),
							collection: selectedCollection.publicKey,
						}),
					)

					if (data) {
						const { txHash } = data
						setBulkNftItems((prev) =>
							prev.map((nftItem) => {
								if (nftItem.id === nft.id) {
									return { ...nftItem, status: 'success', hash: txHash }
								}
								return nftItem
							}),
						)
					}
					if (error) {
						setBulkNftItems((prev) =>
							prev.map((nftItem) => {
								if (nftItem.id === nft.id) {
									return { ...nftItem, status: 'failed' }
								}
								return nftItem
							}),
						)
					}
				}
			}

			// Check if there are any items still in processing state
			const processingItems = bulkNftItems.filter(
				(nft) => nft.status === 'processing',
			).length
			if (processingItems === 0) {
				updateAuthorityMutation.mutate({
					collectionKey: selectedCollection.publicKey,
					newAuthority: wallet.publicKey.toString(),
				})
				// If no items are in processing state, we can set isLoading to false
				setIsLoading(false)
				navigate('/studio/nft/success', {
					pageContext: {
						mint: {
							nft: {
								address: '',
								transactionHash: '',
							},
							collection: {
								address: selectedCollection.publicKey,
							},
							bulkUpload: true,
						},
						collection: selectedCollection
							? {
									address: selectedCollection.publicKey,
								}
							: null,
					},
				})
			}
		} catch (error) {
			// Handle any unexpected errors
			console.log('Error during bulk upload:', error)
			customToast.error('An error occurred during bulk upload')
			setIsLoading(false)
		}
	}

	const handleFileChange = (file: File) => {
		if (file) {
			setNftImage(file)
		}
	}

	const handleCollectionSelect = (address: string | null) => {
		const collection = data.find(
			(collection) => collection.publicKey === address,
		)
		if (collection) {
			setRoyaltyPercentage((collection.royaltyBasisPoints / 100).toString())
			setSelectedCollection(collection)
		}
	}

	return (
		<FormContainer>
			<StudioHeader
				title='Create NFT'
				description='Fill in the details to mint your unique NFT to the blockchain'
			/>

			<div className='space-y-8'>
				<div className='space-y-2'>
					<Label htmlFor='full-name'>Collection (Optional)</Label>
					<CollectionDropdown
						collections={data}
						existingCollectionAddress={pageContext?.collection?.address || null}
						onCollectionSelect={handleCollectionSelect}
						disabled={isLoading}
					/>
					<p className='text-sm text-muted-foreground'>
						You can create an NFT without assigning it to a collection
					</p>
				</div>

				<div className='space-y-2'>
					<Label htmlFor='full-name'>Create Type</Label>
					<RadioSwitch
						values={uploadSwitchValues}
						active={activeUploadType}
						onChange={setActiveUploadType}
					/>
					<p className='text-sm text-muted-foreground'>
						You can upload multiple NFT's at one shot
					</p>
				</div>

				{activeUploadType === 'single-nft' ? (
					<>
						{/* NFT Image */}
						<FileUpload
							id='nft-image'
							label='NFT Image'
							height='h-56'
							onChange={handleFileChange}
							recommendedSize='1000 x 1000px'
							maxSizeMB={5}
							disabled={isLoading}
						/>

						{/* Title */}
						<div className='space-y-2'>
							<Label htmlFor='title'>Title</Label>
							<Input
								id='title'
								placeholder='My Awesome NFT'
								type='text'
								value={title}
								onChange={(e) => setTitle(e.target.value)}
								required
								disabled={isLoading}
							/>
						</div>

						{/* Description */}
						<div className='space-y-2'>
							<Label htmlFor='description'>Description</Label>
							<Textarea
								id='description'
								placeholder='Blockchain Enthusiast Who Loves Art, Blues Music, Tech & Finance'
								value={description}
								onChange={(e) => setDescription(e.target.value)}
								rows={3}
								minLength={250}
								disabled={isLoading}
							/>
						</div>

						{/* Attributes */}
						<AttributeEditor
							attributes={attributes}
							onAddAttribute={addAttribute}
							onRemoveAttribute={removeAttribute}
							onUpdateAttribute={updateAttribute}
							disabled={isLoading}
						/>

						{/* Royalty Percentage */}
						<div className='space-y-2'>
							<Label htmlFor='royalty'>Royalty Percentage</Label>
							<div className='flex items-center'>
								<Input
									id='royalty'
									type='number'
									min='0'
									max='50'
									value={royaltyPercentage}
									onChange={(e) => setRoyaltyPercentage(e.target.value)}
									className='max-w-[100px]'
									disabled={isLoading}
								/>
								<span className='ml-2 text-muted-foreground'>%</span>
							</div>
							<p className='text-sm text-muted-foreground'>
								You receive this percentage of sales when your NFT is sold on
								the secondary market
							</p>
						</div>
					</>
				) : (
					<>
						<div className='space-y-2'>
							<div className='flex justify-between items-center text-sm'>
								<Label>Upload CSV File</Label>
								<button
									className='text-primary text-sm lg:text-sm flex items-center gap-1'
									onClick={(e) => {
										e.preventDefault()
										const link = document.createElement('a')
										link.href = '/example.csv'
										link.download = 'nft_sample.csv'
										document.body.appendChild(link)
										link.click()
										document.body.removeChild(link)
									}}
								>
									<Download className='size-4' />
									<span>Download Sample CSV</span>
								</button>
							</div>

							<FileInput onFileSelect={handleCsvFileSelect} />
						</div>
						<div className='space-y-2 '>
							<Label htmlFor='full-name'>Upload Folder of Images</Label>
							<FolderInput onFolderSelect={handleFolderFilesSelect} />
						</div>
						<NftCollectionTable data={bulkNftItems} />
					</>
				)}

				<div className='flex justify-center pt-4'>
					<Button
						onClick={
							activeUploadType === 'single-nft'
								? handleCreateNFT
								: handleBulkUpload
						}
						disabled={isLoading}
						className='px-8 py-6 w-full max-w-xs'
					>
						{isLoading
							? activeUploadType === 'single-nft'
								? 'Creating NFT...'
								: 'Creating NFT...'
							: activeUploadType === 'single-nft'
								? 'Create NFT'
								: 'Batch Mint'}
					</Button>
				</div>
			</div>
		</FormContainer>
	)
}
