import { getUser } from '@/server/helpers/getUser'
import { prisma } from '@/server/lib/prismaClient'

export interface DeployCollectionParams {
	name: string
	description: string
	royaltyBasisPoints: number
	logoUrl: string
	bannerUrl: string
	metadataUri: string
	collectionAddress: string
	transactionHash: string
	xUrl?: string
	websiteUrl?: string
	tags?: string[]
}

export async function onDeployCollection({
	name,
	description,
	royaltyBasisPoints,
	logoUrl,
	bannerUrl,
	metadataUri,
	collectionAddress,
	transactionHash,
	xUrl,
	websiteUrl,
	tags,
}: DeployCollectionParams) {
	const user = getUser()
	try {
		const collection = await prisma.collection.create({
			data: {
				name,
				description,
				publicKey: collectionAddress,
				ownerId: user.userId,
				royaltyBasisPoints,
				logoUrl,
				bannerUrl,
				metadataUri,
				xUrl,
				websiteUrl,
				tags: tags || [],
				activityLogs: {
					create: {
						type: 'COLLECTION_CREATED',
						transactionHash,
					},
				},
			},
		})

		return { success: true, collection }
	} catch (error) {
		console.error('Error deploying collection:', error)
		return {
			success: false,
			error: error instanceof Error ? error.message : 'Unknown error occurred',
		}
	}
}
