{"editor.formatOnSave": true, "editor.codeActionsOnSave": {"source.fixAll": "explicit", "source.sortMembers": "explicit", "source.fixAll.biome": "explicit", "source.organizeImports.biome": "explicit"}, "editor.formatOnType": false, "editor.formatOnPaste": false, "[javascript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescript]": {"editor.defaultFormatter": "biomejs.biome"}, "[typescriptreact]": {"editor.defaultFormatter": "biomejs.biome"}, "editor.tabSize": 2, "[prisma]": {"editor.defaultFormatter": "Prisma.prisma"}, "cSpell.words": ["<PERSON><PERSON><PERSON>", "devnet", "metaplex", "of<PERSON><PERSON>", "solana", "Solscan", "sonner", "telefunc", "vike"], "files.associations": {"*.css": "tailwindcss"}, "codeium.disableSupercomplete": false, "codeium.enableConfig": {"*": true}, "editor.defaultFormatter": "biomejs.biome"}