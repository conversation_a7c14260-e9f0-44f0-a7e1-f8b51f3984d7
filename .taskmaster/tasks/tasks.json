{"tasks": [{"id": "1", "title": "Phase 1: Critical Authentication & File Management Setup", "description": "Set up the foundation for Phase 1 migration tasks including authentication and file management endpoints", "status": "pending", "priority": "high", "dependencies": [], "subtasks": []}, {"id": "2", "title": "Convert onRefreshToken to REST API endpoint", "description": "Migrate the onRefreshToken function from server/app/auth/auth.telefunc.ts to a REST API endpoint using Hono framework", "status": "pending", "priority": "high", "dependencies": ["1"], "details": "Create POST /api/auth/refresh endpoint with proper JWT validation, session management, and token generation. This is critical for maintaining user authentication sessions.", "testStrategy": "Test token refresh flow, verify new tokens are valid, ensure old sessions are properly invalidated", "subtasks": [{"id": "2.1", "title": "Create Hono route handler for token refresh", "description": "Implement POST /api/auth/refresh endpoint in server/app/auth/auth.route.ts", "status": "pending"}, {"id": "2.2", "title": "Create TanStack Query hook for token refresh", "description": "Create hooks/useRefreshToken.ts with proper error handling", "status": "pending"}, {"id": "2.3", "title": "Update client-side code to use new hook", "description": "Update lib/custom-fetch.ts to use the new REST API endpoint", "status": "pending"}, {"id": "2.4", "title": "Test and cleanup original Telefunc function", "description": "Verify functionality and remove original onRefreshToken from auth.telefunc.ts", "status": "pending"}]}, {"id": "3", "title": "Convert onLogin to REST API endpoint", "description": "Migrate the onLogin function from components/login/LoginButton.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "high", "dependencies": ["2"], "details": "Create POST /api/auth/login endpoint with Solana signature verification, user creation/lookup, and session management", "testStrategy": "Test login flow with valid/invalid signatures, verify user creation for new users, ensure proper session creation", "subtasks": [{"id": "3.1", "title": "Create Hono route handler for login", "description": "Implement POST /api/auth/login endpoint with signature verification", "status": "pending"}, {"id": "3.2", "title": "Create TanStack Query hook for login", "description": "Create hooks/useLogin.ts with proper authentication flow", "status": "pending"}, {"id": "3.3", "title": "Update login components to use new hook", "description": "Update LoginButton and related components to use REST API", "status": "pending"}, {"id": "3.4", "title": "Test and cleanup original Telefunc function", "description": "Verify login functionality and remove original onLogin", "status": "pending"}]}, {"id": "4", "title": "Convert onLogout to REST API endpoint", "description": "Migrate the onLogout function from components/login/LoginButton.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "high", "dependencies": ["3"], "details": "Create POST /api/auth/logout endpoint with session invalidation and proper cleanup", "testStrategy": "Test logout flow, verify sessions are invalidated, ensure proper cleanup of user state", "subtasks": [{"id": "4.1", "title": "Create Hono route handler for logout", "description": "Implement POST /api/auth/logout endpoint with session cleanup", "status": "pending"}, {"id": "4.2", "title": "Create TanStack Query hook for logout", "description": "Create hooks/useLogout.ts with proper session cleanup", "status": "pending"}, {"id": "4.3", "title": "Update logout components to use new hook", "description": "Update logout functionality to use REST API", "status": "pending"}, {"id": "4.4", "title": "Test and cleanup original Telefunc function", "description": "Verify logout functionality and remove original onLogout", "status": "pending"}]}, {"id": "5", "title": "Convert onMe to REST API endpoint", "description": "Migrate the onMe function from server/app/auth/auth.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "high", "dependencies": ["4"], "details": "Create GET /api/auth/me endpoint to retrieve current user profile data with proper authentication", "testStrategy": "Test user profile retrieval, verify authentication requirements, ensure proper user data is returned", "subtasks": [{"id": "5.1", "title": "Create Hono route handler for user profile", "description": "Implement GET /api/auth/me endpoint with authentication middleware", "status": "pending"}, {"id": "5.2", "title": "Create TanStack Query hook for user profile", "description": "Create hooks/useMe.ts with proper caching and error handling", "status": "pending"}, {"id": "5.3", "title": "Update profile data loading to use new hook", "description": "Update edit-profile data client to use REST API", "status": "pending"}, {"id": "5.4", "title": "Test and cleanup original Telefunc function", "description": "Verify user profile functionality and remove original onMe", "status": "pending"}]}, {"id": "6", "title": "Convert onFileUploadPresignedUrl to REST API endpoint", "description": "Migrate the onFileUploadPresignedUrl function from server/app/files/files.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "high", "dependencies": ["5"], "details": "Create POST /api/files/presigned-url endpoint for S3 presigned URL generation with proper validation", "testStrategy": "Test presigned URL generation, verify file upload functionality, ensure proper S3 integration", "subtasks": [{"id": "6.1", "title": "Create Hono route handler for presigned URLs", "description": "Implement POST /api/files/presigned-url endpoint with S3 integration", "status": "pending"}, {"id": "6.2", "title": "Create TanStack Query hook for file uploads", "description": "Create hooks/useFileUpload.ts with proper upload handling", "status": "pending"}, {"id": "6.3", "title": "Update file upload utilities to use new hook", "description": "Update lib/fileUpload.ts to use REST API", "status": "pending"}, {"id": "6.4", "title": "Test and cleanup original Telefunc function", "description": "Verify file upload functionality and remove original function", "status": "pending"}]}, {"id": "7", "title": "Phase 2: User Management & Core Features Setup", "description": "Begin Phase 2 migration focusing on user management and core NFT features", "status": "pending", "priority": "medium", "dependencies": ["6"], "subtasks": []}, {"id": "8", "title": "Convert onGetUserCollections to REST API endpoint", "description": "Migrate the onGetUserCollections function from pages/(protected)/studio/nft/mint/Page.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "medium", "dependencies": ["7"], "details": "Create GET /api/user/collections endpoint to retrieve user's collections for dropdowns and selection", "testStrategy": "Test collection retrieval, verify user authentication, ensure proper collection data format", "subtasks": [{"id": "8.1", "title": "Create Hono route handler for user collections", "description": "Implement GET /api/user/collections endpoint", "status": "pending"}, {"id": "8.2", "title": "Create TanStack Query hook for user collections", "description": "Create hooks/useUserCollections.ts with caching", "status": "pending"}, {"id": "8.3", "title": "Update mint page to use new hook", "description": "Update collection dropdown in mint page to use REST API", "status": "pending"}, {"id": "8.4", "title": "Test and cleanup original Telefunc function", "description": "Verify collection loading and remove original function", "status": "pending"}]}, {"id": "9", "title": "Convert onEditProfile to REST API endpoint", "description": "Migrate the onEditProfile function from pages/(protected)/edit-profile/EditProfileForm.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "medium", "dependencies": ["8"], "details": "Create PUT /api/user/profile endpoint for user profile updates with validation", "testStrategy": "Test profile updates, verify data validation, ensure proper user authentication", "subtasks": [{"id": "9.1", "title": "Create Hono route handler for profile updates", "description": "Implement PUT /api/user/profile endpoint with validation", "status": "pending"}, {"id": "9.2", "title": "Create TanStack Query hook for profile updates", "description": "Create hooks/useEditProfile.ts with mutation handling", "status": "pending"}, {"id": "9.3", "title": "Update edit profile form to use new hook", "description": "Update EditProfileForm to use REST API", "status": "pending"}, {"id": "9.4", "title": "Test and cleanup original Telefunc function", "description": "Verify profile editing and remove original function", "status": "pending"}]}, {"id": "10", "title": "Convert onUpdateUser to REST API endpoint", "description": "Migrate the onUpdateUser function from telefunc/user.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "medium", "dependencies": ["9"], "details": "Create PATCH /api/user endpoint for alternative user profile updates", "testStrategy": "Test user updates, verify field validation, ensure no conflicts with onEditProfile", "subtasks": [{"id": "10.1", "title": "Create Hono route handler for user updates", "description": "Implement PATCH /api/user endpoint", "status": "pending"}, {"id": "10.2", "title": "Create TanStack Query hook for user updates", "description": "Create hooks/useUpdateUser.ts", "status": "pending"}, {"id": "10.3", "title": "Update user update functionality to use new hook", "description": "Update any components using onUpdateUser", "status": "pending"}, {"id": "10.4", "title": "Test and cleanup original Telefunc function", "description": "Verify user updates and remove original function", "status": "pending"}]}, {"id": "11", "title": "Convert onGetNFT to REST API endpoint", "description": "Migrate the onGetNFT function from pages/nft-details/Page.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "medium", "dependencies": ["10"], "details": "Create GET /api/nft/:id endpoint for individual NFT details with complex data relationships", "testStrategy": "Test NFT detail retrieval, verify all related data is included, ensure proper error handling for non-existent NFTs", "subtasks": [{"id": "11.1", "title": "Create Hono route handler for NFT details", "description": "Implement GET /api/nft/:id endpoint with complex data fetching", "status": "pending"}, {"id": "11.2", "title": "Create TanStack Query hook for NFT details", "description": "Create hooks/useNFT.ts with caching and error handling", "status": "pending"}, {"id": "11.3", "title": "Update NFT details page to use new hook", "description": "Update nft-details page and list-for-sale page to use REST API", "status": "pending"}, {"id": "11.4", "title": "Test and cleanup original Telefunc function", "description": "Verify NFT details functionality and remove original function", "status": "pending"}]}, {"id": "12", "title": "Phase 3: Browse & Discovery Features Setup", "description": "Begin Phase 3 migration focusing on browse and discovery features", "status": "pending", "priority": "low", "dependencies": ["11"], "subtasks": []}, {"id": "13", "title": "Convert explore page functions to REST API endpoints", "description": "Migrate onGetNFTs, onGetCollection, and onGetFusionNFTs from pages/explore/Page.telefunc.ts", "status": "pending", "priority": "low", "dependencies": ["12"], "details": "Create GET /api/explore/nfts, GET /api/explore/collections, and GET /api/explore/fusion-nfts endpoints", "testStrategy": "Test browse functionality, verify filtering and pagination, ensure proper data formatting", "subtasks": [{"id": "13.1", "title": "Create Hono route handlers for explore endpoints", "description": "Implement all three explore endpoints with filtering", "status": "pending"}, {"id": "13.2", "title": "Create TanStack Query hooks for explore features", "description": "Create hooks for NFT browsing, collection browsing, and fusion NFTs", "status": "pending"}, {"id": "13.3", "title": "Update explore page to use new hooks", "description": "Update explore page components to use REST API", "status": "pending"}, {"id": "13.4", "title": "Test and cleanup original Telefunc functions", "description": "Verify explore functionality and remove original functions", "status": "pending"}]}, {"id": "14", "title": "Convert onGetTrendingCollections to REST API endpoint", "description": "Migrate the onGetTrendingCollections function from pages/index/Page.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "low", "dependencies": ["13"], "details": "Create GET /api/collections/trending endpoint for homepage trending section", "testStrategy": "Test trending collections retrieval, verify sorting and data accuracy", "subtasks": [{"id": "14.1", "title": "Create Hono route handler for trending collections", "description": "Implement GET /api/collections/trending endpoint", "status": "pending"}, {"id": "14.2", "title": "Create TanStack Query hook for trending collections", "description": "Create hooks/useTrendingCollections.ts", "status": "pending"}, {"id": "14.3", "title": "Update home page to use new hook", "description": "Update index page trending section to use REST API", "status": "pending"}, {"id": "14.4", "title": "Test and cleanup original Telefunc function", "description": "Verify trending functionality and remove original function", "status": "pending"}]}, {"id": "15", "title": "Convert profile page functions to REST API endpoints", "description": "Migrate onGetNFTsByUser, onGetUserCreatedNfts, onUserAssetCounts, and onCheckIfFollowing from pages/profile/Page.telefunc.ts", "status": "pending", "priority": "low", "dependencies": ["14"], "details": "Create multiple profile-related endpoints for user NFTs, created NFTs, asset counts, and follow status", "testStrategy": "Test all profile functionality, verify user-specific data filtering, ensure proper authentication", "subtasks": [{"id": "15.1", "title": "Create Hono route handlers for profile endpoints", "description": "Implement GET /api/user/:id/nfts, /api/user/:id/created-nfts, /api/user/:id/asset-counts, /api/user/:id/follow-status", "status": "pending"}, {"id": "15.2", "title": "Create TanStack Query hooks for profile features", "description": "Create hooks for user NFTs, created NFTs, asset counts, and follow status", "status": "pending"}, {"id": "15.3", "title": "Update profile page to use new hooks", "description": "Update profile page components to use REST API", "status": "pending"}, {"id": "15.4", "title": "Test and cleanup original Telefunc functions", "description": "Verify profile functionality and remove original functions", "status": "pending"}]}, {"id": "16", "title": "Convert onGetNFTActivities to REST API endpoint", "description": "Migrate the onGetNFTActivities function from pages/nft-details/Page.telefunc.ts to a REST API endpoint", "status": "pending", "priority": "low", "dependencies": ["15"], "details": "Create GET /api/nft/:id/activities endpoint for NFT transaction history", "testStrategy": "Test NFT activity retrieval, verify chronological ordering, ensure proper data relationships", "subtasks": [{"id": "16.1", "title": "Create Hono route handler for NFT activities", "description": "Implement GET /api/nft/:id/activities endpoint", "status": "pending"}, {"id": "16.2", "title": "Create TanStack Query hook for NFT activities", "description": "Create hooks/useNFTActivities.ts", "status": "pending"}, {"id": "16.3", "title": "Update NFT details page to use new hook", "description": "Update NFT details activity section to use REST API", "status": "pending"}, {"id": "16.4", "title": "Test and cleanup original Telefunc function", "description": "Verify NFT activities and remove original function", "status": "pending"}]}, {"id": "17", "title": "Final cleanup and validation", "description": "Complete cleanup of all Telefunc files and validate the migration is complete", "status": "pending", "priority": "low", "dependencies": ["16"], "details": "Remove all remaining Telefunc files, update imports, and ensure no broken references remain", "testStrategy": "Full application testing, verify all functionality works with REST API, ensure no Telefunc dependencies remain", "subtasks": [{"id": "17.1", "title": "Remove all Telefunc files", "description": "Delete all .telefunc.ts files that have been fully migrated", "status": "pending"}, {"id": "17.2", "title": "Update any remaining imports", "description": "Ensure no broken imports or references to Telefunc functions", "status": "pending"}, {"id": "17.3", "title": "Full application testing", "description": "Test all major user flows to ensure migration is successful", "status": "pending"}, {"id": "17.4", "title": "Update documentation", "description": "Update any documentation to reflect the new REST API architecture", "status": "pending"}]}, {"id": "18", "title": "Project completion and review", "description": "Final project review and documentation of the completed migration", "status": "pending", "priority": "low", "dependencies": ["17"], "details": "Document the completed migration, create summary of changes, and ensure all success criteria are met", "testStrategy": "Final validation against all success criteria from the PRD", "subtasks": [{"id": "18.1", "title": "Create migration summary documentation", "description": "Document all changes made during the migration", "status": "pending"}, {"id": "18.2", "title": "Validate against success criteria", "description": "Ensure all PRD success criteria have been met", "status": "pending"}, {"id": "18.3", "title": "Performance validation", "description": "Verify that performance is maintained or improved", "status": "pending"}, {"id": "18.4", "title": "Project completion sign-off", "description": "Final sign-off that the migration is complete and successful", "status": "pending"}]}]}