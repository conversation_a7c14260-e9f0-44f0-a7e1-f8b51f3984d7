import { createBackendUmi } from '@/lib/umi'

// Create a singleton Umi instance for server-side use
let umiInstance: ReturnType<typeof createBackendUmi> | null = null

/**
 * Get or create a server-side Umi instance
 * @returns The Umi instance for server-side use
 */
export function getServerUmi() {
	if (!umiInstance) {
		console.log('Creating Umi instance...')
		umiInstance = createBackendUmi(
			process.env.SOLANA_RPC_URL ??
				'https://devnet.helius-rpc.com/?api-key=5ea9c067-957a-4a4f-9208-b6d26a766a6f',
		)
	}
	return umiInstance
}
