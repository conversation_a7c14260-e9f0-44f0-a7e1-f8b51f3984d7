import { ListBucketInventoryConfigurationsOutputFilterSensitiveLog } from '@aws-sdk/client-s3'
import { Hono } from 'hono'
import { ListingStatus, Prisma } from 'prisma-client/client'
import { tryCatch } from '@/lib/try-catch'
import { prisma } from '@/server/lib/prismaClient'
import { authMiddleware } from '@/server/middleware/auth-middleware'
import {
	fetchNftParamSchema,
	nftAfterTransferSchema,
	nftLikeResponseSchema,
	nftLikeSchema,
	nftUnlikeResponseSchema,
} from '@/types/nft.types'
import type { Env } from '../../types/hono-env.types'

const nftRoute = new Hono<Env>()

nftRoute.use('/nft', authMiddleware)

nftRoute.get('/', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		console.log('from not logged in user')
		//return c.json({ message: 'Unauthorized' }, 401)
	}

	const pagination = fetchNftParamSchema.safeParse(c.req.query())
	if (pagination.error) {
		return c.json({ message: 'Invalid query parameters' }, 400)
	}

	const { page, limit, search, sort, collectionId, traits } = pagination.data

	// Build trait filtering conditions
	const whereConditions = []
	if (traits) {
		for (const [traitType, values] of Object.entries(
			traits as Record<string, string[]>,
		)) {
			// Build an OR condition for multiple values of the same trait
			const valueConditions = values.map((value: string) => ({
				attributes: {
					array_contains: [{ trait_type: traitType, value: value }],
				},
			}))

			// If there are multiple values for this trait, use OR between them
			whereConditions.push(
				valueConditions.length > 1
					? { OR: valueConditions }
					: valueConditions[0],
			)
		}
	}

	const skip = (page - 1) * limit

	const where = {
		...(search
			? {
					name: { contains: search, mode: 'insensitive' as const },
				}
			: {}),
		...(collectionId
			? {
					collection: { publicKey: collectionId },
				}
			: {}),
		...(whereConditions.length > 0
			? whereConditions.length > 1
				? { AND: whereConditions }
				: whereConditions[0]
			: {}),
	}

	const sortOrder = sort === 'asc' ? 'asc' : 'desc'

	const { data: nfts, error: nftsError } = await tryCatch(
		prisma.nFT.findMany({
			where,
			skip,
			take: limit,
			orderBy: { createdAt: sortOrder as 'asc' | 'desc' },
			select: {
				id: true,
				publicKey: true,
				createdAt: true,
				name: true,
				imageUrl: true,
				description: true,
				lastBiddingPrice: true,
				attributes: true,
				owner: {
					select: {
						id: true,
						username: true,
						imageUrl: true,
						publicKey: true,
					},
				},
				collection: {
					select: {
						name: true,
					},
				},
				listings: {
					where: {
						status: ListingStatus.ACTIVE,
					},
					select: {
						listingType: true,
						endTime: true,
						status: true,
						price: true,
					},
				},
			},
		}),
	)
	if (nftsError) {
		return c.json({ message: 'Failed to fetch offers made' }, 500)
	}

	const { data: total, error: totalError } = await tryCatch(
		prisma.nFT.count({ where }),
	)

	if (totalError) {
		return c.json({ message: 'Failed to fetch offers count' }, 500)
	}

	if (!nfts || !Array.isArray(nfts)) {
		return c.json({ message: 'Failed to fetch offers' }, 500)
	}

	return c.json(
		{
			nfts,
			total,
			page,
			limit,
		},
		200,
	)
})

nftRoute.post('/like', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const [jsonResult, jsonError] = await tryCatch(c.req.json()).then((res) => [
		res.data,
		res.error,
	])
	if (jsonError) {
		return c.json({ message: 'Invalid request body' }, 400)
	}
	const parsed = nftLikeSchema.safeParse(jsonResult)
	if (!parsed.success) {
		return c.json({ message: 'Invalid request body' }, 400)
	}

	const { publicKey } = parsed.data

	const { data: existingLike, error: findError } = await tryCatch(
		prisma.nftLike.findUnique({
			where: {
				userId_publicKey: {
					userId: user.userId,
					publicKey,
				},
			},
		}),
	)

	if (findError) {
		return c.json({ message: 'Failed to check like status' }, 500)
	}

	if (existingLike) {
		return c.json({ message: 'Already liked' }, 400)
	}

	const { data: like, error: createError } = await tryCatch(
		prisma.nftLike.create({
			data: {
				userId: user.userId,
				publicKey,
			},
		}),
	)

	if (createError || !like) {
		return c.json({ message: 'Failed to like' }, 500)
	}

	const { error: updateLikesError } = await tryCatch(
		prisma.nFT.update({
			where: { publicKey },
			data: { totalLikes: { increment: 1 } },
		}),
	)
	if (updateLikesError) {
		console.error('Failed to increment totalLikes', updateLikesError)
	}

	const validatedResponse = nftLikeResponseSchema.safeParse(like)
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid like response' }, 500)
	}
	return c.json(validatedResponse.data, 201)
})

nftRoute.post('/views/:slug', async (c) => {
	const assetAddress = c.req.param('slug')
	if (!assetAddress) {
		return c.json({ message: 'Invalid request parameter' }, 400)
	}

	const { error: updateViewsError } = await tryCatch(
		prisma.nFT.update({
			where: { publicKey: assetAddress },
			data: { totalViews: { increment: 1 } },
		}),
	)
	if (updateViewsError) {
		console.error('Failed to increment totalViews', updateViewsError)
	}

	return c.json({ message: 'View count incremented' }, 200)
})

nftRoute.delete('/unlike', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const [jsonResult, jsonError] = await tryCatch(c.req.json()).then((res) => [
		res.data,
		res.error,
	])
	if (jsonError) {
		return c.json({ message: 'Invalid request body' }, 400)
	}
	const parsed = nftLikeSchema.safeParse(jsonResult)
	if (!parsed.success) {
		return c.json({ message: 'Invalid request body' }, 400)
	}

	const { publicKey } = parsed.data

	const { error: deleteError } = await tryCatch(
		prisma.nftLike.delete({
			where: {
				userId_publicKey: {
					userId: user.userId,
					publicKey,
				},
			},
		}),
	)

	if (deleteError) {
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		if ((deleteError as any).code === 'P2025') {
			return c.json({ message: 'Not liked' }, 400)
		}
		return c.json({ message: 'Failed to unlike' }, 500)
	}

	const { error: updateLikesError } = await tryCatch(
		prisma.nFT.update({
			where: { publicKey },
			data: { totalLikes: { decrement: 1 } },
		}),
	)
	if (updateLikesError) {
		console.error('Failed to decrement totalLikes', updateLikesError)
	}

	const validatedResponse = nftUnlikeResponseSchema.safeParse({
		message: 'Unliked',
		publicKey,
	})
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid unlike response' }, 500)
	}
	return c.json(validatedResponse.data, 200)
})

/**
 * Type definition for NFT detail response
 */
export type NFTDetailResponse = {
	nft: import('./helper').NFT
	activities?: import('./helper').NFTActivity[]
	relatedNFTs?: import('./helper').NFT[]
	offers?: import('./helper').NFTOffer[]
	priceHistory?: { price: number; createdAt: Date }[]
	listingHistory?: import('./helper').NFTListingHistory[]
	pagination?: {
		currentPage: number
		totalPages: number
		totalItems: number
	}
	userInfo: {
		isLiked: boolean
		isOwner: boolean
	} | null
}

/**
 * Get NFT details by public key
 * @returns JSON response with NFT details or error message
 */
nftRoute.get('/detail/:slug', async (c): Promise<Response> => {
	const user = c.get('user')
	const assetAddress = c.req.param('slug')
	const tab = c.req.query('tab') || 'description'
	const activePage = Number.parseInt(c.req.query('activePage') || '1', 10)
	const limit = Number.parseInt(c.req.query('limit') || '10', 10)

	if (!assetAddress) {
		return c.json({ message: 'Invalid request parameter' }, 400)
	}

	try {
		// Import helper functions from the local helper file
		const {
			getNFTByPublicKey,
			getNFTActivities,
			getRelatedNFTs,
			getNFTOffers,
			getPriceHistory,
			getNFTListingHistory,
		} = await import('./helper')

		const nft = await getNFTByPublicKey(assetAddress, user?.publicKey)

		if (!nft) {
			return c.json({ message: 'NFT not found' }, 404)
		}

		// Add user-specific data if user is authenticated
		let isLiked = false
		if (user?.userId && nft) {
			// First get the NFT ID from the database using the public key
			const dbNft = await prisma.nFT.findFirst({
				where: { publicKey: nft.publicKey },
				select: { id: true },
			})

			if (dbNft) {
				const { data: likeStatus } = await tryCatch(
					prisma.nftLike.findUnique({
						where: {
							userId_publicKey: {
								userId: user.userId,
								publicKey: nft.publicKey,
							},
						},
					}),
				)
				isLiked = !!likeStatus
			}
		}

		// Base response with NFT and user info
		const response: NFTDetailResponse = {
			nft,
			userInfo: user
				? {
						isLiked,
						isOwner:
							nft.owner && 'id' in nft.owner
								? user.userId === nft.owner.id
								: false,
					}
				: null,
		}

		// Fetch additional data based on the selected tab
		switch (tab) {
			case 'description': {
				// For description tab, include related NFTs
				response.relatedNFTs = await getRelatedNFTs(
					assetAddress,
					user?.publicKey || '',
					50,
				)
				break
			}

			case 'priceHistory': {
				// For price history tab, include price history data
				response.priceHistory = await getPriceHistory(assetAddress)
				break
			}

			case 'listing': {
				// For listing tab, include listing history with pagination
				const listingData = await getNFTListingHistory(
					assetAddress,
					limit,
					activePage,
				)
				response.listingHistory = listingData.listings
				response.pagination = {
					currentPage: activePage,
					totalPages: listingData.pages,
					totalItems: listingData.total,
				}
				break
			}

			case 'offers': {
				// For offers tab, include offers with pagination
				const offersData = await getNFTOffers(assetAddress, limit, activePage)
				response.offers = offersData.offers
				response.pagination = {
					currentPage: activePage,
					totalPages: offersData.pages,
					totalItems: offersData.total,
				}
				break
			}

			case 'itemActivity': {
				// For item activity tab, include activities with pagination
				const activitiesData = await getNFTActivities(
					assetAddress,
					limit,
					activePage,
				)
				response.activities = activitiesData.activities
				response.pagination = {
					currentPage: activePage,
					totalPages: activitiesData.pages,
					totalItems: activitiesData.total,
				}
				break
			}

			default: {
				// Default to description tab behavior
				response.relatedNFTs = await getRelatedNFTs(
					assetAddress,
					user?.publicKey || '',
					50,
				)
				break
			}
		}
		return c.json(response, 200)
	} catch (error) {
		console.error('Error fetching NFT details:', error)
		return c.json({ message: 'Failed to fetch NFT details' }, 500)
	}
})

nftRoute.post('/after-transfer', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const [jsonResult, jsonError] = await tryCatch(c.req.json()).then((res) => [
		res.data,
		res.error,
	])
	if (jsonError) {
		return c.json({ message: 'Invalid request body' }, 400)
	}

	const parsed = nftAfterTransferSchema.safeParse(jsonResult)
	if (!parsed.success) {
		return c.json({ message: 'Invalid request body' }, 400)
	}

	const { publicKey, ownerWalletAddress } = parsed.data

	const { data: alreadyExistingUser, error: alreadyExistingUserError } =
		await tryCatch(
			prisma.user.findUnique({
				where: { publicKey: ownerWalletAddress },
				select: { id: true },
			}),
		)

	if (alreadyExistingUserError || !alreadyExistingUser) {
		console.error('User dont exist in our database', alreadyExistingUserError)
		return c.json({ message: 'Failed to update NFT' }, 500)
	}

	const updatedNft = await prisma.nFT.update({
		where: { publicKey },
		data: { ownerId: alreadyExistingUser?.id },
	})

	if (!updatedNft) {
		return c.json({ message: 'Failed to update NFT' }, 500)
	}

	return c.json({ message: 'NFT updated successfully' }, 200)
})

//nftRoute.get('/', async (c) => {
//	const page = Number.parseInt(c.req.query('page') || '1', 10)
//	const limit = Number.parseInt(c.req.query('limit') || '10', 10)
//	const skip = (page - 1) * limit
//
//	try {
//		// Get total count of NFTs
//		const totalItems = await prisma.nFT.count()
//		const totalPages = Math.ceil(totalItems / limit)
//
//		// Fetch NFTs with pagination
//		const nfts = await prisma.nFT.findMany({
//			skip,
//			take: limit,
//			orderBy: { createdAt: 'desc' },
//		})
//
//		return c.json(
//			{
//				nfts,
//				pagination: {
//					currentPage: page,
//					totalPages,
//					totalItems,
//					limit,
//				},
//			},
//			200,
//		)
//	} catch (error) {
//		console.error('Error fetching NFTs:', error)
//		return c.json({ message: 'Failed to fetch NFTs' }, 500)
//	}
//})

export { nftRoute }
