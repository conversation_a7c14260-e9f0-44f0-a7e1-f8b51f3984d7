import { Hono } from 'hono'
import type { Prisma } from 'prisma-client/client'
import { z } from 'zod'
import { tryCatch } from '@/lib/try-catch'
import { prisma } from '@/server/lib/prismaClient'
import { authMiddleware } from '@/server/middleware/auth-middleware'
import { collectionDetailsResponseSchema } from '@/types/collection.types'
import {
	collectionLikeResponseSchema,
	collectionLikeSchema,
	collectionUnlikeResponseSchema,
} from '@/types/collection-like.types'
import type { Env } from '../../types/hono-env.types'

// Zod schemas for activity endpoint query parameters
const activityQuerySchema = z.object({
	activityType: z
		.enum([
			'ALL',
			'COLLECTION_CREATED',
			'NFT_MINTED',
			'NFT_LISTED',
			'NFT_BID_PLACED',
			'NFT_OFFER_CREATED',
			'NFT_OFFER_ACCEPTED',
			'NFT_TRANSFERRED',
		])
		.optional()
		.default('ALL'),
	startDate: z.string().datetime().optional(),
	endDate: z.string().datetime().optional(),
	sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
	page: z.coerce.number().int().min(1).optional().default(1),
	limit: z.coerce.number().int().min(1).max(100).optional().default(10),
})

const collectionRoute = new Hono<Env>()

collectionRoute.use('/collection', authMiddleware)

collectionRoute.post('/views/:slug', async (c) => {
	const assetAddress = c.req.param('slug')
	if (!assetAddress) {
		return c.json({ message: 'Invalid request parameter' }, 400)
	}

	const { error: updateViewsError } = await tryCatch(
		prisma.collection.update({
			where: { publicKey: assetAddress },
			data: { totalViews: { increment: 1 } },
		}),
	)
	if (updateViewsError) {
		console.error('Failed to increment totalViews', updateViewsError)
	}

	return c.json({ message: 'View count incremented' }, 200)
})

// Get like status for a collection
collectionRoute.get('/isLiked/:publicKey', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ isLiked: false }, 200)
	}

	const publicKey = c.req.param('publicKey')
	if (!publicKey) {
		return c.json({ message: 'Collection public key is required' }, 400)
	}

	const { data: likeStatus, error } = await tryCatch(
		prisma.collectionLike.findUnique({
			where: {
				userId_publicKey: {
					userId: user.userId,
					publicKey,
				},
			},
		}),
	)

	if (error) {
		console.error('Error checking collection like status:', error)
		return c.json({ message: 'Failed to check like status' }, 500)
	}

	return c.json({ isLiked: !!likeStatus }, 200)
})

collectionRoute.post('/like', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const [jsonResult, jsonError] = await tryCatch(c.req.json()).then((res) => [
		res.data,
		res.error,
	])
	if (jsonError) {
		return c.json({ message: 'Invalid request body' }, 400)
	}
	const parsed = collectionLikeSchema.safeParse(jsonResult)
	if (!parsed.success) {
		return c.json({ message: 'Invalid request body' }, 400)
	}

	const { publicKey } = parsed.data

	const { data: existingLike, error: findError } = await tryCatch(
		prisma.collectionLike.findUnique({
			where: {
				userId_publicKey: {
					userId: user.userId,
					publicKey,
				},
			},
		}),
	)

	if (findError) {
		return c.json({ message: 'Failed to check like status' }, 500)
	}

	if (existingLike) {
		return c.json({ message: 'Already liked' }, 400)
	}

	const { data: like, error: createError } = await tryCatch(
		prisma.collectionLike.create({
			data: {
				userId: user.userId,
				publicKey,
			},
		}),
	)

	if (createError || !like) {
		return c.json({ message: 'Failed to like' }, 500)
	}

	const { error: updateLikesError } = await tryCatch(
		prisma.collection.update({
			where: { publicKey },
			data: { totalLikes: { increment: 1 } },
		}),
	)
	if (updateLikesError) {
		console.error('Failed to increment totalLikes', updateLikesError)
	}

	const validatedResponse = collectionLikeResponseSchema.safeParse(like)
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid like response' }, 500)
	}
	return c.json(validatedResponse.data, 201)
})

collectionRoute.delete('/unlike', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		return c.json({ message: 'Unauthorized' }, 401)
	}

	const [jsonResult, jsonError] = await tryCatch(c.req.json()).then((res) => [
		res.data,
		res.error,
	])
	if (jsonError) {
		return c.json({ message: 'Invalid request body' }, 400)
	}
	const parsed = collectionLikeSchema.safeParse(jsonResult)
	if (!parsed.success) {
		return c.json({ message: 'Invalid request body' }, 400)
	}

	const { publicKey } = parsed.data

	const { error: deleteError } = await tryCatch(
		prisma.collectionLike.delete({
			where: {
				userId_publicKey: {
					userId: user.userId,
					publicKey,
				},
			},
		}),
	)

	if (deleteError) {
		// biome-ignore lint/suspicious/noExplicitAny: <explanation>
		if ((deleteError as any).code === 'P2025') {
			return c.json({ message: 'Not liked' }, 400)
		}
		return c.json({ message: 'Failed to unlike' }, 500)
	}

	const { error: updateLikesError } = await tryCatch(
		prisma.collection.update({
			where: { publicKey },
			data: { totalLikes: { decrement: 1 } },
		}),
	)
	if (updateLikesError) {
		console.error('Failed to decrement totalLikes', updateLikesError)
	}

	const validatedResponse = collectionUnlikeResponseSchema.safeParse({
		message: 'Unliked',
	})
	if (!validatedResponse.success) {
		return c.json({ message: 'Invalid unlike response' }, 500)
	}
	return c.json(validatedResponse.data, 200)
})

// Define schema for onDeployCollection endpoint
const deployCollectionSchema = z.object({
	name: z.string(),
	description: z.string(),
	royaltyBasisPoints: z.number(),
	logoUrl: z.string().url(),
	bannerUrl: z.string().url(),
	metadataUri: z.string(),
	collectionAddress: z.string(),
	transactionHash: z.string(),
	owner: z.string(),
	xUrl: z.string().optional(),
	websiteUrl: z.string().optional(),
})

type DeployCollectionInput = z.infer<typeof deployCollectionSchema>

collectionRoute.post('/on-deploy', async (c) => {
	// Parse and validate request body
	const [jsonResult, jsonError] = await tryCatch(c.req.json()).then((res) => [
		res.data,
		res.error,
	])
	if (jsonError) {
		return c.json({ message: 'Invalid request body' }, 400)
	}

	// Validate against schema
	const parsed = deployCollectionSchema.safeParse(jsonResult)
	if (!parsed.success) {
		return c.json(
			{
				message: 'Invalid request body',
				errors: parsed.error.format(),
			},
			400,
		)
	}

	const {
		name,
		description,
		royaltyBasisPoints,
		logoUrl,
		bannerUrl,
		metadataUri,
		collectionAddress,
		transactionHash,
		owner,
		xUrl,
		websiteUrl,
	} = parsed.data

	const user = await prisma.user.findUnique({
		where: { publicKey: owner },
		select: { id: true },
	})
	if (!user) {
		return c.json({ message: 'Owner not found' }, 400)
	}

	try {
		// Create the collection in the database
		const { data: collection, error: createError } = await tryCatch(
			prisma.collection.create({
				data: {
					name,
					description,
					publicKey: collectionAddress,
					ownerId: user.id,
					royaltyBasisPoints,
					logoUrl,
					bannerUrl,
					metadataUri,
					xUrl,
					websiteUrl,
					activityLogs: {
						create: {
							type: 'COLLECTION_CREATED',
							transactionHash,
							userId: user.id,
						},
					},
				},
			}),
		)

		if (createError || !collection) {
			console.error('Error creating collection:', createError)
			return c.json(
				{
					success: false,
					message: 'Failed to create collection',
				},
				500,
			)
		}

		return c.json(
			{
				success: true,
				collection,
			},
			201,
		)
	} catch (error) {
		console.error('Error deploying collection:', error)
		return c.json(
			{
				success: false,
				message: 'Failed to create collection',
				error:
					error instanceof Error ? error.message : 'Unknown error occurred',
			},
			500,
		)
	}
})

collectionRoute.get('/:id', async (c) => {
	const user = c.get('user')
	if (!user?.userId) {
		console.log('from not logged in user')
		//return c.json({ message: 'Unauthorized' }, 401)
	}

	const id = c.req.param('id')
	if (!id) {
		return c.json({ message: 'Collection ID is required' }, 400)
	}

	const { data: collectionData, error: collectionError } = await tryCatch(
		prisma.collection.findUnique({
			where: {
				publicKey: id,
			},
			select: {
				id: true,
				name: true,
				bannerUrl: true,
				totalLikes: true,
				nftTraitFilters: true,
				tags: true,
				xUrl: true,
				websiteUrl: true,
				nfts: {
					select: {
						ownerId: true,
					},
				},
			},
		}),
	)

	if (collectionError) {
		console.error('Failed to fetch collection', collectionError)
		return c.json({ message: 'Failed to fetch collection' }, 500)
	}

	if (!collectionData) {
		return c.json({ message: 'Collection not found' }, 404)
	}

	let isLiked = false

	if (user?.userId) {
		const { data: userLike, error: userLikeError } = await tryCatch(
			prisma.collectionLike.findUnique({
				where: {
					userId_publicKey: {
						userId: user.userId,
						publicKey: id,
					},
				},
			}),
		)
		if (userLikeError) {
			console.error('Failed to check like status', userLikeError)
		}
		isLiked = !!userLike
	}

	const responseData = {
		...collectionData,
		nftCount: collectionData?.nfts?.length,
		ownerCount: new Set(collectionData.nfts.map((n) => n.ownerId)).size,
		isLiked: isLiked,
	}

	const validatedResponse =
		collectionDetailsResponseSchema.safeParse(responseData)
	if (!validatedResponse.success) {
		console.error(
			'Invalid collection details response',
			validatedResponse.error,
		)
		return c.json({ message: 'Invalid collection details response' }, 500)
	}

	return c.json(validatedResponse.data, 200)
})

collectionRoute.get('/:id/activity', async (c) => {
	const id = c.req.param('id')
	if (!id) {
		return c.json({ message: 'Collection ID is required' }, 400)
	}

	const queryParseResult = activityQuerySchema.safeParse(c.req.query())
	if (!queryParseResult.success) {
		return c.json(
			{
				message: 'Invalid query parameters',
				errors: queryParseResult.error.format(),
			},
			400,
		)
	}

	const { activityType, startDate, endDate, sortOrder, page, limit } =
		queryParseResult.data

	const { data: collection, error: collectionError } = await tryCatch(
		prisma.collection.findUnique({
			where: { publicKey: id },
			select: { id: true }, // Select only the ID to check for existence
		}),
	)

	if (collectionError) {
		console.error('Error fetching collection:', collectionError)
		return c.json({ message: 'Error fetching collection data' }, 500)
	}

	if (!collection) {
		return c.json({ message: 'Collection not found' }, 404)
	}

	const where: Prisma.ActivityLogWhereInput = {
		collectionId: collection.id,
		type: { not: 'ROYALTY_RECEIVED' },
	}
	if (activityType !== 'ALL') {
		where.type = activityType
	}
	if (startDate || endDate) {
		where.createdAt = {}
		if (startDate) {
			where.createdAt.gte = new Date(startDate)
		}
		if (endDate) {
			where.createdAt.lte = new Date(endDate)
		}
	}

	const { data: activities, error: activitiesError } = await tryCatch(
		prisma.activityLog.findMany({
			where,
			orderBy: {
				createdAt: sortOrder as 'asc' | 'desc',
			},
			skip: (page - 1) * limit,
			take: limit,
			include: {
				nft: true,
				fromUser: true,
				toUser: true,
			},
		}),
	)
	if (activitiesError) {
		console.error('Error fetching activities:', activitiesError)
		return c.json({ message: 'Error fetching activities' }, 500)
	}

	return c.json({
		activities: activities.map((activity) => ({
			...activity,
			amount:
				activity.data &&
				typeof activity.data === 'object' &&
				'amount' in activity.data
					? Number(activity.data.amount || 0)
					: null,
		})),
		//activities,
		pagination: {
			currentPage: page,
			limit,
			totalPages: Math.ceil(activities.length / limit),
			totalActivities: activities.length,
		},
	})
})

collectionRoute.get('/:id/price-chart', async (c) => {
	const id = c.req.param('id')
	if (!id) {
		return c.json({ message: 'Collection ID is required' }, 400)
	}

	const { data: collection, error: collectionError } = await tryCatch(
		prisma.collection.findUnique({
			where: { publicKey: id },
			select: { id: true }, // Select only the ID to check for existence
		}),
	)

	if (collectionError) {
		console.error('Error fetching collection:', collectionError)
		return c.json({ message: 'Error fetching collection data' }, 500)
	}

	if (!collection) {
		return c.json({ message: 'Collection not found' }, 404)
	}

	const orders = await prisma.order.findMany({
		where: {
			collectionId: collection.id,
			status: 'COMPLETED',
		},
		orderBy: {
			createdAt: 'asc',
		},
		select: {
			price: true,
			createdAt: true,
		},
	})
	return c.json(orders)
})

export { collectionRoute }
