# Technology Stack

- **Frontend Framework**: Vike (latest stable version) with vike-react for React 19 integration.
- **UI Library**: React 19 (latest stable version), using functional components and hooks with optimized memoization patterns.
- **Styling**: Tailwind CSS (latest stable version), with Shadcn UI for pre-built components.
- **NFT Integration**: Metaplex SDK for Solana NFT interactions and metadata fetching.
- **Serverless Functions**: Telefunc (latest stable version) for defining server-side functions.
- **Web Framework**: Hono (latest stable version) for server-side routing and middleware.
- **ORM**: Prisma (latest stable version) for database interactions, configured for Cloudflare Workers.
- **Deployment**: Cloudflare Workers, using wrangler for deployment.
- **<PERSON><PERSON> and <PERSON>atter**: <PERSON>iome (latest stable version), configured for JavaScript, TypeScript, and CSS.
- **Package Manager**: Bun (latest stable version), used for managing dependencies and scripts.

# NFT Marketplace Specific Guidelines

## NFT Asset Implementation

- Use the EnhancedAsset type that combines Metaplex AssetV1 objects with corresponding metadata.
- Implement proper error handling for metadata fetching.
- Build comprehensive data objects that can be directly consumed by UI components.
- Follow the pattern established in the EnhancedNFT asset data structure implementation.

## Authentication and Wallet Connection

- Implement auto login after wallet connection.
- Use optimized memoization patterns with useCallback for better performance.
- Follow React 19 best practices by using minimal dependencies in hook dependency arrays.
- Ensure the login component aligns with React 19 compiler optimizations.

## Specific Instructions

### Vike

- Use Vike for routing and page definitions.
- Each page should have a `+Page.tsx` file in the `/pages` directory.
- Use `+config.ts` for page-specific configurations if needed.
- Follow Vike's documentation for SSR and SSG setups, available at [Vike Docs](https://vike.dev/).

### React

- Write functional components using React 19 hooks.
- Prefer composition over inheritance.
- Use TypeScript for type safety, ensuring all props and state are typed.
- Implement optimized memoization patterns using useCallback.

### NFT Integration

- Use Metaplex SDK for Solana NFT interactions.
- Implement comprehensive error handling for NFT metadata fetching.
- Create reusable hooks for common NFT operations.
- Cache NFT metadata where appropriate to improve performance.

### Tailwind CSS

- Apply Tailwind classes directly in JSX for styling.
- Use Shadcn UI components where possible to maintain consistency, as seen at [Shadcn UI](https://ui.shadcn.com/).
- Ensure that Tailwind classes are sorted using Biome's `useSortedClasses` rule, detailed at [Biome Tailwind Rule](https://biomejs.dev/linter/rules/use-sorted-classes/).

### Telefunc

- Define serverless functions in `/telefunc` directory with `.telefunc.ts` extension.
- Use Telefunc's API to handle requests and responses, following [Telefunc Docs](https://telefunc.com/).
- Ensure functions are optimized for Cloudflare Workers, considering latency and bundle size.

### Hono

- Set up Hono in `/server/index.ts` for server-side routing.
- Use Hono's middleware for authentication, logging, etc., as described at [Hono Docs](https://hono.dev/).
- Integrate with Telefunc functions as needed for server-side logic.

### Prisma

- Define database schema in `/prisma/schema.prisma`.
- Use Prisma Client for database queries, ensuring compatibility with Cloudflare Workers, as noted at [Prisma Cloudflare Docs](https://www.prisma.io/docs/deployment/cloudflare-workers).
- Consider using Prisma's edge-compatible drivers or HTTP-based connections for performance.

### Cloudflare

- Configure deployment with `wrangler.toml`, following [Cloudflare Workers Docs](https://developers.cloudflare.com/workers/).
- Manage environment variables and secrets through Cloudflare dashboard.
- Optimize bundle size to stay within Cloudflare's limits, typically 1MB for Workers.

### Biome

- Configure Biome in `biome.json` with recommended rules, using the `recommended` preset, as seen at [Biome Config](https://biomejs.dev/reference/configuration/).
- Enable linting for JavaScript, TypeScript, and CSS, ensuring consistency.
- Use Biome's formatter for consistent code style, with 2-space indentation.

### Bun

- Use Bun for installing dependencies: run `bun install`.
- Run scripts with Bun, e.g., `bun run build`, as detailed at [Bun Docs](https://bun.sh/docs).
- Ensure all tools are compatible with Bun, checking for any known issues.

# Coding Conventions

- **Quotes**: Use single quotes for strings.
- **Indentation**: Use 2 spaces for indentation.
- **Semicolons**: Optional, but be consistent across the project.
- **Variable Naming**: Use camelCase for variables and functions, PascalCase for components.
- **File Naming**: Use kebab-case for file names, e.g., `my-component.tsx`.
- **TypeScript**: Use TypeScript for all code, with strict mode enabled in `tsconfig.json`.
- **React Components**: Use functional components with hooks, avoid class components for modern practices.
- **Tailwind Classes**: Keep classes sorted using Biome's `useSortedClasses` rule.
- **Error Handling**: Implement proper error handling in functions, especially for NFT metadata fetching and wallet connections, using try-catch blocks.
- **Testing**: Write unit tests for critical components and functions using a testing framework like Jest or Vitest, with tests in `/tests/` directory.

# Project Structure

- `/pages/`: Vike page files (`+Page.tsx`, `+config.ts`).
- `/components/`: Shared React components, organized by feature, e.g., `/components/ui/`, `/components/nft/`.
- `/hooks/`: Custom React hooks for NFT operations and other reusable logic.
- `/types/`: Shared TypeScript types and interfaces, including the EnhancedAsset type.
- `/telefunc/`: Telefunc serverless functions (`*.telefunc.ts`), grouped by functionality.
- `/server/`: Hono server setup (`index.ts`) and related middleware.
- `/prisma/`: Prisma schema (`schema.prisma`), migrations, and seed data, with scripts in `/prisma/scripts/`.
- `/public/`: Static assets like images and fonts.
- `/config/`: Configuration files like `biome.json`, `tailwind.config.js`, `wrangler.toml`.
- `/utils/`: Utility functions, especially for NFT-related operations.

# Additional Notes

- **NFT Integration**: Ensure all NFT operations follow best practices for the Solana blockchain and Metaplex standards.
- **Wallet Connection**: Implement secure wallet connection with proper error handling and user feedback.
- **Type Safety**: Ensure all code is type-safe using TypeScript. Define types for props, state, API responses, and NFT data structures to prevent runtime errors.
- **Environment Variables**: Use environment variables for sensitive information like API keys. Manage them through Cloudflare's dashboard, as described at [Cloudflare Secrets](https://developers.cloudflare.com/workers/platform/environment-variables/).
- **Performance**: Optimize for performance, especially for NFT data loading and display. Implement lazy loading and caching where appropriate.
- **Security**: Follow best practices for security, such as validating inputs, using HTTPS, and avoiding direct database access from the client, as outlined at [Cloudflare Security](https://developers.cloudflare.com/workers/security/).
- **Dependency Management**: Regularly update dependencies to their latest versions using Bun, e.g., `bun update`. Check for vulnerabilities using tools like `bun audit`.
- **Documentation**: Maintain inline documentation for complex logic and functions, especially for NFT-related operations. Use JSDoc for TypeScript to improve readability and IDE support.